import { StyleSheet, Text, View, ScrollView, Image, Pressable, Dimensions } from "react-native";
import { useRouter } from "expo-router";
import { FileText, Heart } from "lucide-react-native";
import { useFilteredContent, useContent, useTranslation } from "@/hooks/useContentStore";
import AgeFilter from "@/components/AgeFilter";
import Colors from "@/constants/colors";

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function ParentHubScreen() {
    const router = useRouter();
    const { filteredContent } = useFilteredContent();
    const { addToRecentlyViewed, toggleFavorite, isFavorite } = useContent();
    const t = useTranslation();

    const parentHubContent = filteredContent.filter(
        item => item.category === 'Parent Hub'
    );

    const handleContentPress = (id: string) => {
        const item = parentHubContent.find(item => item.id === id);
        if (item) {
            addToRecentlyViewed(item);
            router.push(`/content/${id}`);
        }
    };

    const handleFavoritePress = (id: string) => {
        toggleFavorite(id);
    };

    return (
        <ScrollView style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.headerTitle}>{t('parentHub')}</Text>
                <Text style={styles.headerSubtitle}>
                    {t('expertGuidance')}
                </Text>
            </View>

            <AgeFilter />

            <View style={styles.content}>
                {parentHubContent.length > 0 ? (
                    parentHubContent.map(item => (
                        <Pressable
                            key={item.id}
                            style={styles.resourceCard}
                            onPress={() => handleContentPress(item.id)}
                        >
                            <Image
                                source={{ uri: item.thumbnail }}
                                style={styles.resourceImage}
                                resizeMode="cover"
                            />
                            <View style={styles.resourceContent}>
                                <View style={styles.resourceHeader}>
                                    <Text style={styles.resourceTitle}>{item.title}</Text>
                                    <Pressable
                                        style={styles.favoriteButton}
                                        onPress={() => handleFavoritePress(item.id)}
                                        hitSlop={10}
                                    >
                                        <Heart
                                            size={20}
                                            color={isFavorite(item.id) ? Colors.error : Colors.textSecondary}
                                            fill={isFavorite(item.id) ? Colors.error : 'transparent'}
                                        />
                                    </Pressable>
                                </View>
                                <Text style={styles.resourceDescription} numberOfLines={3}>
                                    {item.description}
                                </Text>
                                <View style={styles.resourceFooter}>
                                    <Text style={styles.resourceMeta}>{item.duration}</Text>
                                    <View style={styles.resourceBadge}>
                                        <Text style={styles.resourceBadgeText}>{item.ageGroup}</Text>
                                    </View>
                                </View>
                                <Pressable
                                    style={styles.readButton}
                                    onPress={() => handleContentPress(item.id)}
                                >
                                    <FileText size={16} color={Colors.text} />
                                    <Text style={styles.readButtonText}>{t('readGuide')}</Text>
                                </Pressable>
                            </View>
                        </Pressable>
                    ))
                ) : (
                    <View style={styles.emptyContainer}>
                        <Text style={styles.emptyText}>
                            {t('noParentResourcesAvailable')}
                        </Text>
                    </View>
                )}
            </View>
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    header: {
        padding: 16,
    },
    headerTitle: {
        color: Colors.text,
        fontSize: 24,
        fontWeight: '700' as const,
        marginBottom: 8,
    },
    headerSubtitle: {
        color: Colors.textSecondary,
        fontSize: 16,
        marginBottom: 16,
    },
    content: {
        padding: 16,
    },
    resourceCard: {
        backgroundColor: Colors.card,
        borderRadius: 12,
        overflow: 'hidden',
        marginBottom: 20,
    },
    resourceImage: {
        width: '100%',
        height: 180,
    },
    resourceContent: {
        padding: 16,
    },
    resourceHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 8,
    },
    resourceTitle: {
        color: Colors.text,
        fontSize: 18,
        fontWeight: '700' as const,
        flex: 1,
    },
    favoriteButton: {
        padding: 4,
    },
    resourceDescription: {
        color: Colors.textSecondary,
        fontSize: 14,
        lineHeight: 20,
        marginBottom: 16,
    },
    resourceFooter: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
    },
    resourceMeta: {
        color: Colors.textSecondary,
        fontSize: 14,
    },
    resourceBadge: {
        backgroundColor: 'rgba(255,127,80,0.2)',
        paddingHorizontal: 10,
        paddingVertical: 4,
        borderRadius: 12,
    },
    resourceBadgeText: {
        color: Colors.primary,
        fontSize: 12,
        fontWeight: '600' as const,
    },
    readButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.primary,
        paddingHorizontal: 16,
        paddingVertical: 10,
        borderRadius: 8,
        alignSelf: 'flex-start',
    },
    readButtonText: {
        color: Colors.text,
        fontSize: 14,
        fontWeight: '600' as const,
        marginLeft: 8,
    },
    emptyContainer: {
        padding: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    emptyText: {
        color: Colors.textSecondary,
        fontSize: 16,
        textAlign: 'center',
    },
});