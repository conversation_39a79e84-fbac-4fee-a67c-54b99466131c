import { StyleSheet, Text, View, ScrollView, FlatList, Dimensions } from "react-native";
import { useFilteredContent, useTranslation } from "@/hooks/useContentStore";
import ContentCard from "@/components/ContentCard";
import AgeFilter from "@/components/AgeFilter";
import Colors from "@/constants/colors";

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function StorytimeScreen() {
    const { filteredContent } = useFilteredContent();
    const t = useTranslation();

    const storytimeContent = filteredContent.filter(
        item => item.category === 'Storytime'
    );

    const cardWidth = (SCREEN_WIDTH - 32 - 10) / 2;

    return (
        <View style={styles.container}>
            <ScrollView style={styles.scrollContainer}>
                <Text style={styles.headerTitle}>{t('storytimeSouthAfrica')}</Text>
                <Text style={styles.headerSubtitle}>
                    {t('exploreBeautifulStories')}
                </Text>

                <AgeFilter />

                <View style={styles.contentGrid}>
                    {storytimeContent.length > 0 ? (
                        <FlatList
                            data={storytimeContent}
                            keyExtractor={(item) => item.id}
                            numColumns={2}
                            scrollEnabled={false}
                            renderItem={({ item }) => (
                                <View style={styles.cardContainer}>
                                    <ContentCard
                                        item={item}
                                        width={cardWidth}
                                        height={150}
                                    />
                                </View>
                            )}
                        />
                    ) : (
                        <View style={styles.emptyContainer}>
                            <Text style={styles.emptyText}>
                                {t('noStoriesAvailable')}
                            </Text>
                        </View>
                    )}
                </View>
            </ScrollView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    scrollContainer: {
        flex: 1,
        padding: 16,
    },
    headerTitle: {
        color: Colors.text,
        fontSize: 24,
        fontWeight: '700' as const,
        marginBottom: 8,
    },
    headerSubtitle: {
        color: Colors.textSecondary,
        fontSize: 16,
        marginBottom: 24,
    },
    contentGrid: {
        marginTop: 16,
    },
    cardContainer: {
        width: '50%',
        padding: 5,
    },
    emptyContainer: {
        padding: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    emptyText: {
        color: Colors.textSecondary,
        fontSize: 16,
        textAlign: 'center',
    },
});