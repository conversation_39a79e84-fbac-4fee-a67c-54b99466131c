import { StyleSheet, Text, View, ScrollView, Image, Pressable, Dimensions } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { useState, useEffect } from "react";
import { Heart, ArrowLeft, Share2, Play } from "lucide-react-native";
import { useContent, useTranslation } from "@/hooks/useContentStore";
import YouTubePlayer from "@/components/YouTubePlayer";
import Colors from "@/constants/colors";
import { StatusBar } from "expo-status-bar";
import * as Haptics from "expo-haptics";
import { Platform } from "react-native";

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function ContentDetailScreen() {
    const { id } = useLocalSearchParams<{ id: string }>();
    const router = useRouter();
    const { getContentById, toggleFavorite, isFavorite } = useContent();
    const t = useTranslation();
    const [content, setContent] = useState(getContentById(id));
    const [favorite, setFavorite] = useState(false);
    const [showVideo, setShowVideo] = useState(false);

    useEffect(() => {
        if (content) {
            setFavorite(isFavorite(content.id));
        }
    }, [content, isFavorite]);

    if (!content) {
        return (
            <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{t('contentNotFound')}</Text>
                <Pressable style={styles.backButton} onPress={() => router.back()}>
                    <Text style={styles.backButtonText}>{t('goBack')}</Text>
                </Pressable>
            </View>
        );
    }

    const handleFavoritePress = () => {
        if (Platform.OS !== 'web') {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
        toggleFavorite(content.id);
        setFavorite(!favorite);
    };

    const handleBackPress = () => {
        router.back();
    };

    const handlePlayPress = () => {
        setShowVideo(true);
    };

    const getPlayButtonText = () => {
        switch (content.category) {
            case 'Storytime':
                return t('watchStory');
            case 'Lullabies':
                return t('playSong');
            case 'Bath Time':
                return t('startBathTime');
            case 'Parent Hub':
                return t('watchGuide');
            default:
                return t('playContent');
        }
    };

    return (
        <View style={styles.container}>
            <StatusBar style="light" />

            <ScrollView showsVerticalScrollIndicator={false}>
                <View style={styles.imageContainer}>
                    <Image
                        source={{ uri: content.thumbnail }}
                        style={styles.image}
                        resizeMode="cover"
                    />
                    <View style={styles.overlay}>
                        <View style={styles.headerButtons}>
                            <Pressable
                                style={styles.iconButton}
                                onPress={handleBackPress}
                                hitSlop={10}
                            >
                                <ArrowLeft size={24} color={Colors.text} />
                            </Pressable>
                            <View style={styles.rightButtons}>
                                <Pressable
                                    style={styles.iconButton}
                                    hitSlop={10}
                                >
                                    <Share2 size={24} color={Colors.text} />
                                </Pressable>
                                <Pressable
                                    style={styles.iconButton}
                                    onPress={handleFavoritePress}
                                    hitSlop={10}
                                >
                                    <Heart
                                        size={24}
                                        color={favorite ? Colors.error : Colors.text}
                                        fill={favorite ? Colors.error : 'transparent'}
                                    />
                                </Pressable>
                            </View>
                        </View>
                        {!showVideo && content.youtubeId && (
                            <View style={styles.playOverlay}>
                                <Pressable style={styles.playButtonOverlay} onPress={handlePlayPress}>
                                    <Play size={48} color={Colors.text} />
                                </Pressable>
                            </View>
                        )}
                    </View>
                </View>

                <View style={styles.contentContainer}>
                    <View style={styles.titleContainer}>
                        <Text style={styles.title}>{content.title}</Text>
                        <View style={styles.metaContainer}>
                            <View style={styles.badge}>
                                <Text style={styles.badgeText}>{content.category}</Text>
                            </View>
                            <Text style={styles.meta}>{content.ageGroup}</Text>
                            {content.duration && (
                                <Text style={styles.meta}>{content.duration}</Text>
                            )}
                        </View>
                    </View>

                    <Text style={styles.description}>{content.description}</Text>

                    <View style={styles.languagesContainer}>
                        <Text style={styles.languagesTitle}>{t('availableIn')}</Text>
                        <View style={styles.languagesList}>
                            {content.languages.map(language => (
                                <View key={language} style={styles.languageItem}>
                                    <Text style={styles.languageText}>{language}</Text>
                                </View>
                            ))}
                        </View>
                    </View>

                    {content.youtubeId && showVideo && (
                        <View style={styles.videoContainer}>
                            <YouTubePlayer videoId={content.youtubeId} />
                        </View>
                    )}

                    {content.youtubeId && !showVideo && (
                        <Pressable style={styles.playButton} onPress={handlePlayPress}>
                            <Play size={20} color={Colors.text} />
                            <Text style={styles.playButtonText}>
                                {getPlayButtonText()}
                            </Text>
                        </Pressable>
                    )}

                    <View style={styles.divider} />

                    <Text style={styles.sectionTitle}>{t('moreLikeThis')}</Text>

                    <Text style={styles.comingSoon}>
                        {t('comingSoon')}
                    </Text>
                </View>
            </ScrollView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
        backgroundColor: Colors.background,
    },
    errorText: {
        color: Colors.text,
        fontSize: 18,
        marginBottom: 20,
    },
    backButton: {
        backgroundColor: Colors.primary,
        paddingHorizontal: 20,
        paddingVertical: 10,
        borderRadius: 8,
    },
    backButtonText: {
        color: Colors.text,
        fontSize: 16,
        fontWeight: '600' as const,
    },
    imageContainer: {
        width: SCREEN_WIDTH,
        height: SCREEN_WIDTH * 0.75,
    },
    image: {
        width: '100%',
        height: '100%',
    },
    overlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(0,0,0,0.3)',
    },
    headerButtons: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: 16,
    },
    iconButton: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: 10,
    },
    rightButtons: {
        flexDirection: 'row',
    },
    playOverlay: {
        ...StyleSheet.absoluteFillObject,
        justifyContent: 'center',
        alignItems: 'center',
        top: 60,
    },
    playButtonOverlay: {
        width: 80,
        height: 80,
        borderRadius: 40,
        backgroundColor: 'rgba(255, 127, 80, 0.9)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    contentContainer: {
        padding: 16,
    },
    titleContainer: {
        marginBottom: 16,
    },
    title: {
        color: Colors.text,
        fontSize: 24,
        fontWeight: '700' as const,
        marginBottom: 12,
    },
    metaContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
    },
    badge: {
        backgroundColor: Colors.primary,
        paddingHorizontal: 10,
        paddingVertical: 4,
        borderRadius: 4,
        marginRight: 12,
    },
    badgeText: {
        color: Colors.text,
        fontSize: 12,
        fontWeight: '600' as const,
    },
    meta: {
        color: Colors.textSecondary,
        fontSize: 14,
        marginRight: 12,
    },
    description: {
        color: Colors.textSecondary,
        fontSize: 16,
        lineHeight: 24,
        marginBottom: 24,
    },
    languagesContainer: {
        marginBottom: 24,
    },
    languagesTitle: {
        color: Colors.text,
        fontSize: 16,
        fontWeight: '600' as const,
        marginBottom: 12,
    },
    languagesList: {
        flexDirection: 'row',
        flexWrap: 'wrap',
    },
    languageItem: {
        backgroundColor: Colors.card,
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        marginRight: 8,
        marginBottom: 8,
    },
    languageText: {
        color: Colors.textSecondary,
        fontSize: 14,
    },
    videoContainer: {
        marginBottom: 24,
    },
    playButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.primary,
        paddingVertical: 14,
        paddingHorizontal: 20,
        borderRadius: 8,
        alignSelf: 'flex-start',
        marginBottom: 24,
    },
    playButtonText: {
        color: Colors.text,
        fontSize: 16,
        fontWeight: '600' as const,
        marginLeft: 8,
    },
    divider: {
        height: 1,
        backgroundColor: 'rgba(255,255,255,0.1)',
        marginBottom: 24,
    },
    sectionTitle: {
        color: Colors.text,
        fontSize: 18,
        fontWeight: '700' as const,
        marginBottom: 16,
    },
    comingSoon: {
        color: Colors.textSecondary,
        fontSize: 16,
        fontStyle: 'italic' as const,
        textAlign: 'center',
        marginVertical: 20,
    },
});