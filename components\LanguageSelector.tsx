import { StyleSheet, Text, View, Pressable, Modal } from 'react-native';
import { useState } from 'react';
import { Globe } from 'lucide-react-native';
import { Language } from '@/types';
import { LANGUAGES } from '@/constants/mockData';
import Colors from '@/constants/colors';
import { useContent, useTranslation } from '@/hooks/useContentStore';

export default function LanguageSelector() {
    const [modalVisible, setModalVisible] = useState(false);
    const { selectedLanguage, changeLanguage } = useContent();
    const t = useTranslation();

    const handleLanguageSelect = (language: Language) => {
        changeLanguage(language);
        setModalVisible(false);
    };

    const getLanguageFlag = (language: Language) => {
        switch (language) {
            case 'English': return '🇬🇧';
            case 'isiZulu': return '🇿🇦';
            case 'isiXhosa': return '🇿🇦';
            case 'Afrikaans': return '🇿🇦';
            case 'Sepedi': return '🇿🇦';
            case 'Sesotho': return '🇿🇦';
            default: return '🌍';
        }
    };

    return (
        <View>
            <Pressable
                style={styles.button}
                onPress={() => setModalVisible(true)}
            >
                <Text style={styles.flag}>{getLanguageFlag(selectedLanguage)}</Text>
                <Text style={styles.buttonText}>{selectedLanguage}</Text>
            </Pressable>

            <Modal
                animationType="fade"
                transparent={true}
                visible={modalVisible}
                onRequestClose={() => setModalVisible(false)}
            >
                <Pressable
                    style={styles.modalOverlay}
                    onPress={() => setModalVisible(false)}
                >
                    <View style={styles.modalContent}>
                        <Text style={styles.modalTitle}>{t('selectLanguage')}</Text>

                        {LANGUAGES.map((language) => (
                            <Pressable
                                key={language}
                                style={[
                                    styles.languageItem,
                                    selectedLanguage === language && styles.selectedLanguage
                                ]}
                                onPress={() => handleLanguageSelect(language)}
                            >
                                <Text style={styles.languageFlag}>{getLanguageFlag(language)}</Text>
                                <Text
                                    style={[
                                        styles.languageText,
                                        selectedLanguage === language && styles.selectedLanguageText
                                    ]}
                                >
                                    {language}
                                </Text>
                            </Pressable>
                        ))}
                    </View>
                </Pressable>
            </Modal>
        </View>
    );
}

const styles = StyleSheet.create({
    button: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.card,
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 20,
        shadowColor: Colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 1,
        shadowRadius: 4,
        elevation: 3,
    },
    flag: {
        fontSize: 16,
        marginRight: 6,
    },
    buttonText: {
        color: Colors.text,
        fontSize: 14,
        fontWeight: '600' as const,
    },
    modalOverlay: {
        flex: 1,
        backgroundColor: Colors.overlay,
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: Colors.card,
        borderRadius: 20,
        padding: 24,
        width: '85%',
        maxWidth: 320,
        shadowColor: Colors.shadow,
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 1,
        shadowRadius: 16,
        elevation: 10,
    },
    modalTitle: {
        color: Colors.text,
        fontSize: 20,
        fontWeight: '700' as const,
        marginBottom: 20,
        textAlign: 'center',
    },
    languageItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 14,
        paddingHorizontal: 16,
        borderRadius: 12,
        marginBottom: 8,
    },
    selectedLanguage: {
        backgroundColor: Colors.primary,
    },
    languageFlag: {
        fontSize: 20,
        marginRight: 12,
    },
    languageText: {
        color: Colors.text,
        fontSize: 16,
        fontWeight: '500' as const,
    },
    selectedLanguageText: {
        color: Colors.card,
        fontWeight: '700' as const,
    },
});