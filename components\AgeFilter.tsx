import { StyleSheet, Text, View, ScrollView, Pressable } from 'react-native';
import { AgeGroup } from '@/types';
import { AGE_GROUPS } from '@/constants/mockData';
import Colors from '@/constants/colors';
import { useContent, useTranslation } from '@/hooks/useContentStore';

export default function AgeFilter() {
    const { selectedAgeGroup, changeAgeGroup } = useContent();
    const t = useTranslation();

    const handleSelect = (ageGroup: AgeGroup) => {
        if (selectedAgeGroup === ageGroup) {
            changeAgeGroup(null);
        } else {
            changeAgeGroup(ageGroup);
        }
    };

    const getAgeEmoji = (ageGroup: AgeGroup) => {
        switch (ageGroup) {
            case '0-12 months': return '👶';
            case '1-3 years': return '🧒';
            case '3-5 years': return '👧';
            case 'All ages': return '👨‍👩‍👧‍👦';
            default: return '👶';
        }
    };

    return (
        <View style={styles.container}>
            <Text style={styles.title}>{t('filterByAge')}</Text>
            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.filterContainer}
            >
                {AGE_GROUPS.map((ageGroup) => (
                    <Pressable
                        key={ageGroup}
                        style={[
                            styles.filterItem,
                            selectedAgeGroup === ageGroup && styles.selectedFilter
                        ]}
                        onPress={() => handleSelect(ageGroup)}
                    >
                        <Text style={styles.emoji}>{getAgeEmoji(ageGroup)}</Text>
                        <Text
                            style={[
                                styles.filterText,
                                selectedAgeGroup === ageGroup && styles.selectedFilterText
                            ]}
                        >
                            {ageGroup}
                        </Text>
                    </Pressable>
                ))}
            </ScrollView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        marginBottom: 20,
        paddingHorizontal: 16,
    },
    title: {
        color: Colors.text,
        fontSize: 18,
        fontWeight: '700' as const,
        marginBottom: 12,
    },
    filterContainer: {
        paddingVertical: 8,
    },
    filterItem: {
        backgroundColor: Colors.card,
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderRadius: 25,
        marginRight: 12,
        borderWidth: 2,
        borderColor: 'transparent',
        alignItems: 'center',
        minWidth: 80,
        shadowColor: Colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 1,
        shadowRadius: 4,
        elevation: 3,
    },
    selectedFilter: {
        backgroundColor: Colors.primary,
        borderColor: Colors.accent,
    },
    emoji: {
        fontSize: 20,
        marginBottom: 4,
    },
    filterText: {
        color: Colors.text,
        fontSize: 12,
        fontWeight: '600' as const,
        textAlign: 'center',
    },
    selectedFilterText: {
        color: Colors.card,
        fontWeight: '700' as const,
    },
});