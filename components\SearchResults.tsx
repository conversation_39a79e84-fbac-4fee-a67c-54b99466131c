import { StyleSheet, Text, View, FlatList, Dimensions } from 'react-native';
import { ContentItem } from '@/types';
import ContentCard from './ContentCard';
import Colors from '@/constants/colors';
import { useTranslation } from '@/hooks/useContentStore';

interface SearchResultsProps {
    results: ContentItem[];
    query: string;
}

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function SearchResults({ results, query }: SearchResultsProps) {
    const t = useTranslation();
    const cardWidth = (SCREEN_WIDTH - 48) / 2;

    if (!query) {
        return null;
    }

    return (
        <View style={styles.container}>
            <Text style={styles.title}>
                {results.length > 0
                    ? `${results.length} ${t('resultsFor')} "${query}"`
                    : `${t('noResultsFor')} "${query}"`
                }
            </Text>

            {results.length > 0 ? (
                <FlatList
                    data={results}
                    keyExtractor={(item) => item.id}
                    numColumns={2}
                    scrollEnabled={false}
                    contentContainerStyle={styles.grid}
                    renderItem={({ item }) => (
                        <View style={styles.cardContainer}>
                            <ContentCard
                                item={item}
                                width={cardWidth}
                                height={140}
                            />
                        </View>
                    )}
                />
            ) : (
                <View style={styles.emptyContainer}>
                    <Text style={styles.emptyText}>{t('tryDifferentSearch')}</Text>
                </View>
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        padding: 16,
    },
    title: {
        color: Colors.text,
        fontSize: 18,
        fontWeight: '700' as const,
        marginBottom: 16,
    },
    grid: {
        paddingBottom: 20,
    },
    cardContainer: {
        width: '50%',
        padding: 8,
    },
    emptyContainer: {
        padding: 40,
        alignItems: 'center',
    },
    emptyText: {
        color: Colors.textSecondary,
        fontSize: 16,
        textAlign: 'center',
    },
});