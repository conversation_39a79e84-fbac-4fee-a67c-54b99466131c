import { StyleSheet, Text, View, Image, Pressable, Dimensions } from 'react-native';
import { useRouter } from 'expo-router';
import { Heart, Play } from 'lucide-react-native';
import { ContentItem } from '@/types';
import Colors from '@/constants/colors';
import { useContent } from '@/hooks/useContentStore';

interface ContentCardProps {
    item: ContentItem;
    width?: number;
    height?: number;
    featured?: boolean;
}

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function ContentCard({
    item,
    width = SCREEN_WIDTH * 0.4,
    height = SCREEN_WIDTH * 0.25,
    featured = false
}: ContentCardProps) {
    const router = useRouter();
    const { addToRecentlyViewed, toggleFavorite, isFavorite } = useContent();
    const favorite = isFavorite(item.id);

    const handlePress = () => {
        addToRecentlyViewed(item);
        router.push(`/content/${item.id}`);
    };

    const handleFavoritePress = () => {
        toggleFavorite(item.id);
    };

    return (
        <View style={[
            styles.container,
            { width: featured ? SCREEN_WIDTH * 0.85 : width }
        ]}>
            <Pressable
                style={[styles.card, { height: featured ? height * 1.5 : height }]}
                onPress={handlePress}
                android_ripple={{ color: 'rgba(255,255,255,0.1)' }}
            >
                <Image
                    source={{ uri: item.thumbnail }}
                    style={styles.image}
                    resizeMode="cover"
                />
                <View style={styles.overlay}>
                    <View style={styles.playButton}>
                        <Play size={24} color={Colors.card} fill={Colors.primary} />
                    </View>
                    <View style={styles.contentInfo}>
                        <Text style={styles.title} numberOfLines={1}>{item.title}</Text>
                        {item.duration && (
                            <Text style={styles.duration}>{item.duration}</Text>
                        )}
                    </View>
                </View>
                <Pressable
                    style={styles.favoriteButton}
                    onPress={handleFavoritePress}
                    hitSlop={10}
                >
                    <Heart
                        size={18}
                        color={favorite ? Colors.error : Colors.card}
                        fill={favorite ? Colors.error : 'transparent'}
                    />
                </Pressable>
                {featured && (
                    <View style={styles.featuredBadge}>
                        <Text style={styles.featuredText}>⭐ Featured</Text>
                    </View>
                )}
                <View style={styles.ageGroup}>
                    <Text style={styles.ageGroupText}>{item.ageGroup}</Text>
                </View>
            </Pressable>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        marginRight: 12,
        marginBottom: 12,
    },
    card: {
        borderRadius: 16,
        overflow: 'hidden',
        backgroundColor: Colors.card,
        shadowColor: Colors.shadow,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 1,
        shadowRadius: 8,
        elevation: 5,
    },
    image: {
        width: '100%',
        height: '100%',
    },
    overlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(0,0,0,0.3)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    playButton: {
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: 'rgba(255,255,255,0.9)',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 'auto',
        marginTop: 'auto',
    },
    contentInfo: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        padding: 12,
        backgroundColor: 'rgba(0,0,0,0.7)',
    },
    title: {
        color: Colors.card,
        fontWeight: '700' as const,
        fontSize: 14,
    },
    duration: {
        color: Colors.card,
        fontSize: 12,
        marginTop: 4,
        opacity: 0.9,
    },
    favoriteButton: {
        position: 'absolute',
        top: 12,
        right: 12,
        backgroundColor: 'rgba(0,0,0,0.6)',
        borderRadius: 20,
        padding: 8,
    },
    featuredBadge: {
        position: 'absolute',
        top: 12,
        left: 12,
        backgroundColor: Colors.accent,
        borderRadius: 12,
        paddingHorizontal: 10,
        paddingVertical: 6,
    },
    featuredText: {
        color: Colors.text,
        fontSize: 10,
        fontWeight: '700' as const,
    },
    ageGroup: {
        position: 'absolute',
        bottom: 12,
        right: 12,
        backgroundColor: Colors.primary,
        borderRadius: 8,
        paddingHorizontal: 8,
        paddingVertical: 4,
    },
    ageGroupText: {
        color: Colors.card,
        fontSize: 10,
        fontWeight: '600' as const,
    },
});