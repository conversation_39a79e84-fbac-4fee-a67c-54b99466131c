import { StyleSheet, Text, View, ScrollView, Image, Pressable, FlatList } from "react-native";
import { useRouter } from "expo-router";
import { Play, Pause } from "lucide-react-native";
import { useState } from "react";
import { useFilteredContent, useContent, useTranslation } from "@/hooks/useContentStore";
import AgeFilter from "@/components/AgeFilter";
import Colors from "@/constants/colors";

export default function LullabiesScreen() {
    const router = useRouter();
    const { filteredContent } = useFilteredContent();
    const { addToRecentlyViewed } = useContent();
    const t = useTranslation();
    const [playingId, setPlayingId] = useState<string | null>(null);

    const lullabiesContent = filteredContent.filter(
        item => item.category === 'Lullabies'
    );

    const handlePlay = (id: string) => {
        if (playingId === id) {
            setPlayingId(null);
        } else {
            setPlayingId(id);
        }
    };

    const handleContentPress = (id: string) => {
        const item = lullabiesContent.find(item => item.id === id);
        if (item) {
            addToRecentlyViewed(item);
            router.push(`/content/${id}`);
        }
    };

    return (
        <View style={styles.container}>
            <ScrollView>
                <View style={styles.header}>
                    <Text style={styles.headerTitle}>{t('lullabiesLearningSongs')}</Text>
                    <Text style={styles.headerSubtitle}>
                        {t('soothingMelodies')}
                    </Text>
                </View>

                <AgeFilter />

                <View style={styles.content}>
                    {lullabiesContent.length > 0 ? (
                        <FlatList
                            data={lullabiesContent}
                            keyExtractor={(item) => item.id}
                            scrollEnabled={false}
                            renderItem={({ item }) => (
                                <Pressable
                                    style={styles.songCard}
                                    onPress={() => handleContentPress(item.id)}
                                >
                                    <Image
                                        source={{ uri: item.thumbnail }}
                                        style={styles.songImage}
                                        resizeMode="cover"
                                    />
                                    <View style={styles.songInfo}>
                                        <Text style={styles.songTitle}>{item.title}</Text>
                                        <Text style={styles.songDescription} numberOfLines={2}>
                                            {item.description}
                                        </Text>
                                        <View style={styles.songMeta}>
                                            <Text style={styles.songDuration}>{item.duration}</Text>
                                            <Text style={styles.songLanguages}>
                                                {item.languages.join(', ')}
                                            </Text>
                                        </View>
                                    </View>
                                    <Pressable
                                        style={styles.playButton}
                                        onPress={() => handlePlay(item.id)}
                                    >
                                        {playingId === item.id ? (
                                            <Pause size={24} color={Colors.text} />
                                        ) : (
                                            <Play size={24} color={Colors.text} />
                                        )}
                                    </Pressable>
                                </Pressable>
                            )}
                        />
                    ) : (
                        <View style={styles.emptyContainer}>
                            <Text style={styles.emptyText}>
                                {t('noLullabiesAvailable')}
                            </Text>
                        </View>
                    )}
                </View>
            </ScrollView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    header: {
        padding: 16,
    },
    headerTitle: {
        color: Colors.text,
        fontSize: 24,
        fontWeight: '700' as const,
        marginBottom: 8,
    },
    headerSubtitle: {
        color: Colors.textSecondary,
        fontSize: 16,
        marginBottom: 16,
    },
    content: {
        padding: 16,
    },
    songCard: {
        flexDirection: 'row',
        backgroundColor: Colors.card,
        borderRadius: 12,
        overflow: 'hidden',
        marginBottom: 16,
        height: 100,
    },
    songImage: {
        width: 100,
        height: '100%',
    },
    songInfo: {
        flex: 1,
        padding: 12,
        justifyContent: 'space-between',
    },
    songTitle: {
        color: Colors.text,
        fontSize: 16,
        fontWeight: '600' as const,
    },
    songDescription: {
        color: Colors.textSecondary,
        fontSize: 12,
    },
    songMeta: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    songDuration: {
        color: Colors.textSecondary,
        fontSize: 12,
    },
    songLanguages: {
        color: Colors.primary,
        fontSize: 12,
    },
    playButton: {
        width: 50,
        height: '100%',
        backgroundColor: 'rgba(255,127,80,0.2)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyContainer: {
        padding: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    emptyText: {
        color: Colors.textSecondary,
        fontSize: 16,
        textAlign: 'center',
    },
});