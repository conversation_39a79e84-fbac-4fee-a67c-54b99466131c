import { StyleSheet, Text, View, TextInput, Pressable, Animated } from 'react-native';
import { useState, useRef, useEffect } from 'react';
import { Search, X } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useTranslation } from '@/hooks/useContentStore';

interface SearchBarProps {
    onSearch: (query: string) => void;
    placeholder?: string;
}

export default function SearchBar({ onSearch, placeholder }: SearchBarProps) {
    const [query, setQuery] = useState('');
    const [isExpanded, setIsExpanded] = useState(false);
    const animatedWidth = useRef(new Animated.Value(50)).current;
    const t = useTranslation();

    const expandSearch = () => {
        setIsExpanded(true);
        Animated.timing(animatedWidth, {
            toValue: 250,
            duration: 300,
            useNativeDriver: false,
        }).start();
    };

    const collapseSearch = () => {
        setIsExpanded(false);
        setQuery('');
        onSearch('');
        Animated.timing(animatedWidth, {
            toValue: 50,
            duration: 300,
            useNativeDriver: false,
        }).start();
    };

    const handleSearch = (text: string) => {
        setQuery(text);
        onSearch(text);
    };

    return (
        <Animated.View style={[styles.container, { width: animatedWidth }]}>
            {!isExpanded ? (
                <Pressable style={styles.searchButton} onPress={expandSearch}>
                    <Search size={20} color={Colors.text} />
                </Pressable>
            ) : (
                <View style={styles.expandedContainer}>
                    <Search size={18} color={Colors.textSecondary} style={styles.searchIcon} />
                    <TextInput
                        style={styles.input}
                        value={query}
                        onChangeText={handleSearch}
                        placeholder={placeholder || t('searchContent')}
                        placeholderTextColor={Colors.textSecondary}
                        autoFocus={true}
                        returnKeyType="search"
                    />
                    <Pressable style={styles.closeButton} onPress={collapseSearch}>
                        <X size={18} color={Colors.textSecondary} />
                    </Pressable>
                </View>
            )}
        </Animated.View>
    );
}

const styles = StyleSheet.create({
    container: {
        height: 44,
        backgroundColor: Colors.card,
        borderRadius: 22,
        shadowColor: Colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 1,
        shadowRadius: 4,
        elevation: 3,
    },
    searchButton: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Colors.accent,
        borderRadius: 22,
    },
    expandedContainer: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
    },
    searchIcon: {
        marginRight: 8,
    },
    input: {
        flex: 1,
        fontSize: 16,
        color: Colors.text,
        fontWeight: '500' as const,
    },
    closeButton: {
        padding: 4,
        marginLeft: 8,
    },
});