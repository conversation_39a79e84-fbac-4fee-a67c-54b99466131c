import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import React, { useEffect } from "react";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { StatusBar } from "expo-status-bar";
import { ContentProvider } from "@/hooks/useContentStore";
import Colors from "@/constants/colors";
import "../global.css";

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

const queryClient = new QueryClient();

function RootLayoutNav() {
    return (
        <Stack
            screenOptions={{
                headerBackTitle: "Back",
                headerStyle: { backgroundColor: Colors.background },
                headerTintColor: Colors.text,
                headerTitleStyle: { fontWeight: "700" },
                contentStyle: { backgroundColor: Colors.background }
            }}
        >
            <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
            <Stack.Screen
                name="content/[id]"
                options={{
                    title: "Content Details",
                    presentation: "card"
                }}
            />
        </Stack>
    );
}

export default function RootLayout() {
    useEffect(() => {
        SplashScreen.hideAsync();
    }, []);

    return (
        <QueryClientProvider client={queryClient}>
            <ContentProvider>
                <GestureHandlerRootView style={{ flex: 1 }}>
                    <StatusBar style="dark" />
                    <RootLayoutNav />
                </GestureHandlerRootView>
            </ContentProvider>
        </QueryClientProvider>
    );
}