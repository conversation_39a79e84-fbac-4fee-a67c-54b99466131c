import { AgeGroup, CategorySection, ContentCategory, ContentItem, Language } from "@/types";

export const LANGUAGES: Language[] = [
    'English', 'isiZulu', 'isiXhosa', 'Afrikaans', 'Sepedi', 'Sesotho'
];

export const AGE_GROUPS: AgeGroup[] = [
    '0-12 months', '1-3 years', '3-5 years', 'All ages'
];

export const CONTENT_ITEMS: ContentItem[] = [
    // Storytime
    {
        id: 'story-1',
        title: 'The Lion and the Mouse',
        description: 'A classic tale of friendship between a mighty lion and a tiny mouse, teaching children about kindness and helping others.',
        thumbnail: 'https://images.unsplash.com/photo-1615485290382-441e4d049cb5?q=80&w=1000',
        ageGroup: '3-5 years',
        languages: ['English', 'isiZulu', 'Afrikaans'],
        category: 'Storytime',
        duration: '5 min',
        featured: true,
        youtubeId: 'dQw4w9WgXcQ'
    },
    {
        id: 'story-2',
        title: '<PERSON>\'s Magic',
        description: 'Join Mama <PERSON> as she shares the magic of storytelling with her grandchildren under the African sky.',
        thumbnail: 'https://images.unsplash.com/photo-1594608661623-aa0bd3a69799?q=80&w=1000',
        ageGroup: '1-3 years',
        languages: ['English', 'isiXhosa', 'Sesotho'],
        category: 'Storytime',
        duration: '7 min',
        youtubeId: 'jNQXAC9IVRw'
    },
    {
        id: 'story-3',
        title: 'Bedtime with Bhuti',
        description: 'A soothing bedtime story about a little boy named Bhuti and his adventures in dreamland.',
        thumbnail: 'https://images.unsplash.com/photo-1544776193-52d3932b8e86?q=80&w=1000',
        ageGroup: '0-12 months',
        languages: ['English', 'isiZulu'],
        category: 'Storytime',
        duration: '4 min',
        youtubeId: 'M7lc1UVf-VE'
    },
    {
        id: 'story-4',
        title: 'Ubuntu and Me',
        description: 'Learn about the concept of Ubuntu - "I am because we are" through this beautifully illustrated story.',
        thumbnail: 'https://images.unsplash.com/photo-1607453998774-d533f65dac99?q=80&w=1000',
        ageGroup: '3-5 years',
        languages: ['English', 'isiXhosa', 'Afrikaans'],
        category: 'Storytime',
        duration: '6 min',
        youtubeId: 'YQHsXMglC9A'
    },

    // Lullabies
    {
        id: 'lullaby-1',
        title: 'Thula Baba',
        description: 'A traditional South African lullaby to soothe your baby to sleep.',
        thumbnail: 'https://images.unsplash.com/photo-1492725764893-90b379c2b6e7?q=80&w=1000',
        ageGroup: '0-12 months',
        languages: ['isiZulu', 'English'],
        category: 'Lullabies',
        duration: '3 min',
        featured: true,
        youtubeId: 'kJQP7kiw5Fk'
    },
    {
        id: 'lullaby-2',
        title: 'African Dreamland',
        description: 'Gentle melodies inspired by African wildlife to help your little one drift off to sleep.',
        thumbnail: 'https://images.unsplash.com/photo-1533738363-b7f9aef128ce?q=80&w=1000',
        ageGroup: '0-12 months',
        languages: ['English'],
        category: 'Lullabies',
        duration: '5 min',
        youtubeId: 'L_jWHffIx5E'
    },
    {
        id: 'lullaby-3',
        title: 'Counting Stars',
        description: 'Count the stars in the African sky with this soothing bedtime song.',
        thumbnail: 'https://images.unsplash.com/photo-1527556897228-d7e4b2a5f7a2?q=80&w=1000',
        ageGroup: '1-3 years',
        languages: ['English', 'Afrikaans'],
        category: 'Lullabies',
        duration: '4 min',
        youtubeId: 'fJ9rUzIMcZQ'
    },

    // Play & Learn
    {
        id: 'play-1',
        title: 'Animal Sounds Safari',
        description: 'Learn about African animals and the sounds they make in this interactive game.',
        thumbnail: 'https://images.unsplash.com/photo-1516466723877-e4ec1d736c8a?q=80&w=1000',
        ageGroup: '1-3 years',
        languages: ['English', 'isiZulu', 'Afrikaans'],
        category: 'Play & Learn',
        duration: '8 min',
        featured: true,
        youtubeId: 'hFZFjoX2cGg'
    },
    {
        id: 'play-2',
        title: 'Counting with Fruits',
        description: 'Learn to count from 1 to 10 with colorful South African fruits.',
        thumbnail: 'https://images.unsplash.com/photo-1610832958506-aa56368176cf?q=80&w=1000',
        ageGroup: '1-3 years',
        languages: ['English', 'isiXhosa', 'Sepedi'],
        category: 'Play & Learn',
        duration: '6 min',
        youtubeId: 'BQ0mxQXmLsk'
    },
    {
        id: 'play-3',
        title: 'DIY Bottle Shakers',
        description: 'Create musical instruments using recycled bottles and household items.',
        thumbnail: 'https://images.unsplash.com/photo-1503454537195-1dcabb73ffb9?q=80&w=1000',
        ageGroup: '3-5 years',
        languages: ['English'],
        category: 'Play & Learn',
        duration: '10 min',
        youtubeId: 'ZbZSe6N_BXs'
    },

    // Bath Time
    {
        id: 'bath-1',
        title: 'Splish Splash Fun',
        description: 'Join Mama and Baby for a fun and calming bath time routine with bubbles and songs.',
        thumbnail: 'https://images.unsplash.com/photo-1544113155-56be94b57249?q=80&w=1000',
        ageGroup: '0-12 months',
        languages: ['English', 'isiZulu'],
        category: 'Bath Time',
        duration: '7 min',
        featured: true,
        youtubeId: 'PHgc8Q6qTjc'
    },
    {
        id: 'bath-2',
        title: 'Rubber Ducky Adventures',
        description: 'Follow the adventures of Ducky as he sails through bath time with your little one.',
        thumbnail: 'https://images.unsplash.com/photo-1565538810643-b5bdb714032a?q=80&w=1000',
        ageGroup: '1-3 years',
        languages: ['English', 'Afrikaans'],
        category: 'Bath Time',
        duration: '5 min',
        youtubeId: 'A_MjCqQoLLA'
    },
    {
        id: 'bath-3',
        title: 'Bath Time Lullabies',
        description: 'Gentle lullabies to create a soothing bath time experience for your baby.',
        thumbnail: 'https://images.unsplash.com/photo-1471286174890-9c112ffca5b4?q=80&w=1000',
        ageGroup: '0-12 months',
        languages: ['English', 'isiXhosa'],
        category: 'Bath Time',
        duration: '8 min',
        youtubeId: 'SX_ViT4Ra7k'
    },

    // Parent Hub
    {
        id: 'parent-1',
        title: 'Baby Sleep Guide',
        description: 'Expert tips to help your baby sleep better and establish healthy sleep routines.',
        thumbnail: 'https://images.unsplash.com/photo-1590486803833-1c5dc8ddd4c8?q=80&w=1000',
        ageGroup: '0-12 months',
        languages: ['English'],
        category: 'Parent Hub',
        duration: '12 min',
        featured: true,
        youtubeId: 'YE7VzlLtp-4'
    },
    {
        id: 'parent-2',
        title: 'Nutrition for Toddlers',
        description: 'Learn about balanced nutrition and meal ideas for growing toddlers.',
        thumbnail: 'https://images.unsplash.com/photo-1505576399279-565b52d4ac71?q=80&w=1000',
        ageGroup: '1-3 years',
        languages: ['English', 'isiZulu', 'Afrikaans'],
        category: 'Parent Hub',
        duration: '15 min',
        youtubeId: 'NUsoVlDFqZg'
    },
    {
        id: 'parent-3',
        title: 'School Readiness',
        description: 'Prepare your child for their first day of school with these helpful tips.',
        thumbnail: 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?q=80&w=1000',
        ageGroup: '3-5 years',
        languages: ['English', 'isiXhosa', 'Sesotho'],
        category: 'Parent Hub',
        duration: '10 min',
        youtubeId: 'FTQbiNvZqaY'
    }
];

export const FEATURED_CONTENT: ContentItem[] = CONTENT_ITEMS.filter(item => item.featured);

export const CONTENT_SECTIONS: CategorySection[] = [
    {
        title: 'Featured For You',
        category: 'Storytime',
        items: FEATURED_CONTENT
    },
    {
        title: 'Storytime South Africa',
        category: 'Storytime',
        items: CONTENT_ITEMS.filter(item => item.category === 'Storytime')
    },
    {
        title: 'Lullabies & Learning Songs',
        category: 'Lullabies',
        items: CONTENT_ITEMS.filter(item => item.category === 'Lullabies')
    },
    {
        title: 'Play & Learn Activities',
        category: 'Play & Learn',
        items: CONTENT_ITEMS.filter(item => item.category === 'Play & Learn')
    },
    {
        title: 'Bath Time',
        category: 'Bath Time',
        items: CONTENT_ITEMS.filter(item => item.category === 'Bath Time')
    },
    {
        title: 'Parent Hub',
        category: 'Parent Hub',
        items: CONTENT_ITEMS.filter(item => item.category === 'Parent Hub')
    }
];