import { StyleSheet, Text, View, FlatList, Dimensions } from 'react-native';
import { ContentItem } from '@/types';
import ContentCard from './ContentCard';
import Colors from '@/constants/colors';

interface ContentCarouselProps {
    title: string;
    items: ContentItem[];
    featured?: boolean;
}

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function ContentCarousel({ title, items, featured = false }: ContentCarouselProps) {
    if (items.length === 0) {
        return null;
    }

    return (
        <View style={styles.container}>
            <Text style={styles.sectionTitle}>{title}</Text>
            <FlatList
                data={items}
                keyExtractor={(item) => item.id}
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.listContent}
                renderItem={({ item }) => (
                    <ContentCard
                        item={item}
                        featured={featured && item.featured}
                        width={featured ? SCREEN_WIDTH * 0.85 : SCREEN_WIDTH * 0.4}
                        height={featured ? SCREEN_WIDTH * 0.4 : SCREEN_WIDTH * 0.25}
                    />
                )}
            />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        marginBottom: 24,
    },
    sectionTitle: {
        color: Colors.text,
        fontSize: 18,
        fontWeight: '700' as const,
        marginBottom: 12,
        marginLeft: 16,
    },
    listContent: {
        paddingHorizontal: 16,
    },
});