import { StyleSheet, Text, View, Dimensions, Image, Pressable } from 'react-native';
import { useRouter } from 'expo-router';
import { ContentItem } from '@/types';
import { Play, Star } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useContent, useTranslation } from '@/hooks/useContentStore';

interface FeaturedContentProps {
    item: ContentItem;
}

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export default function FeaturedContent({ item }: FeaturedContentProps) {
    const router = useRouter();
    const { addToRecentlyViewed } = useContent();
    const t = useTranslation();

    const handlePress = () => {
        addToRecentlyViewed(item);
        router.push(`/content/${item.id}`);
    };

    return (
        <View style={styles.container}>
            <Pressable style={styles.card} onPress={handlePress}>
                <Image
                    source={{ uri: item.thumbnail }}
                    style={styles.image}
                    resizeMode="cover"
                />
                <View style={styles.overlay}>
                    <View style={styles.content}>
                        <View style={styles.featuredBadge}>
                            <Star size={16} color={Colors.text} fill={Colors.accent} />
                            <Text style={styles.featuredText}>{t('featured')}</Text>
                        </View>
                        <Text style={styles.title}>{item.title}</Text>
                        <Text style={styles.description} numberOfLines={2}>
                            {item.description}
                        </Text>
                        <View style={styles.metaContainer}>
                            <View style={styles.ageBadge}>
                                <Text style={styles.ageText}>{item.ageGroup}</Text>
                            </View>
                            {item.duration && (
                                <Text style={styles.duration}>{item.duration}</Text>
                            )}
                        </View>
                        <Pressable style={styles.playButton} onPress={handlePress}>
                            <Play size={20} color={Colors.card} fill={Colors.card} />
                            <Text style={styles.playText}>{t('play')}</Text>
                        </Pressable>
                    </View>
                </View>
            </Pressable>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: 16,
        marginBottom: 24,
    },
    card: {
        width: '100%',
        height: SCREEN_HEIGHT * 0.4,
        borderRadius: 20,
        overflow: 'hidden',
        shadowColor: Colors.shadow,
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 1,
        shadowRadius: 16,
        elevation: 10,
    },
    image: {
        width: '100%',
        height: '100%',
    },
    overlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'flex-end',
    },
    content: {
        padding: 24,
    },
    featuredBadge: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.accent,
        alignSelf: 'flex-start',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        marginBottom: 16,
    },
    featuredText: {
        color: Colors.text,
        fontSize: 12,
        fontWeight: '700' as const,
        marginLeft: 4,
    },
    title: {
        color: Colors.card,
        fontSize: 26,
        fontWeight: '800' as const,
        marginBottom: 8,
    },
    description: {
        color: Colors.card,
        fontSize: 16,
        marginBottom: 16,
        opacity: 0.9,
    },
    metaContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 20,
    },
    ageBadge: {
        backgroundColor: Colors.primary,
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 12,
        marginRight: 16,
    },
    ageText: {
        color: Colors.card,
        fontSize: 12,
        fontWeight: '600' as const,
    },
    duration: {
        color: Colors.card,
        fontSize: 14,
        opacity: 0.8,
    },
    playButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.primary,
        paddingHorizontal: 24,
        paddingVertical: 14,
        borderRadius: 25,
        alignSelf: 'flex-start',
        shadowColor: Colors.shadow,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 1,
        shadowRadius: 8,
        elevation: 5,
    },
    playText: {
        color: Colors.card,
        fontSize: 16,
        fontWeight: '700' as const,
        marginLeft: 8,
    },
});