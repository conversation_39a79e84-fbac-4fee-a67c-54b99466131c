import { Tabs } from "expo-router";
import { Home, Bath, Book, Music, User } from "lucide-react-native";
import React from "react";
import Colors from "@/constants/colors";
import { useTranslation } from "@/hooks/useContentStore";

export default function TabLayout() {
  const t = useTranslation();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors.primary,
        tabBarInactiveTintColor: Colors.inactive,
        tabBarStyle: {
          backgroundColor: Colors.card,
          borderTopColor: Colors.primary,
          borderTopWidth: 2,
          paddingTop: 8,
          paddingBottom: 8,
          height: 70,
          shadowColor: Colors.shadow,
          shadowOffset: { width: 0, height: -4 },
          shadowOpacity: 1,
          shadowRadius: 8,
          elevation: 10,
        },
        headerStyle: {
          backgroundColor: Colors.background,
          shadowColor: 'transparent',
          elevation: 0,
        },
        headerTintColor: Colors.text,
        headerTitleStyle: {
          fontWeight: '700',
          fontSize: 20,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
          marginTop: 4,
        },
        tabBarIconStyle: {
          marginTop: 4,
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: t('home'),
          tabBarIcon: ({ color }) => <Home color={color} size={24} />,
        }}
      />
      <Tabs.Screen
        name="storytime"
        options={{
          title: t('storytime'),
          tabBarIcon: ({ color }) => <Book color={color} size={24} />,
        }}
      />
      <Tabs.Screen
        name="bath-time"
        options={{
          title: t('bathTime'),
          tabBarIcon: ({ color }) => <Bath color={color} size={24} />,
        }}
      />
      <Tabs.Screen
        name="lullabies"
        options={{
          title: t('lullabies'),
          tabBarIcon: ({ color }) => <Music color={color} size={24} />,
        }}
      />
      <Tabs.Screen
        name="parent-hub"
        options={{
          title: t('parentHub'),
          tabBarIcon: ({ color }) => <User color={color} size={24} />,
        }}
      />
    </Tabs>
  );
}