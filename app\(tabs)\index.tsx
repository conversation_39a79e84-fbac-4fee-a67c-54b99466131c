import { StyleSheet, Text, View, ScrollView, Image } from "react-native";
import { useMemo, useState } from "react";
import { useFilteredContent, useFeaturedContent, useTranslation, useContent, useSearchResults } from "@/hooks/useContentStore";
import ContentCarousel from "@/components/ContentCarousel";
import AgeFilter from "@/components/AgeFilter";
import LanguageSelector from "@/components/LanguageSelector";
import FeaturedContent from "@/components/FeaturedContent";
import SearchBar from "@/components/SearchBar";
import SearchResults from "@/components/SearchResults";
import { CONTENT_SECTIONS } from "@/constants/mockData";
import Colors from "@/constants/colors";

export default function HomeScreen() {
    const { filteredContent } = useFilteredContent();
    const { updateSearchQuery } = useContent();
    const { searchResults, searchQuery } = useSearchResults();
    const featuredContent = useFeaturedContent();
    const t = useTranslation();

    const featuredItem = useMemo(() => {
        return featuredContent.length > 0 ? featuredContent[0] : null;
    }, [featuredContent]);

    const filteredSections = useMemo(() => {
        return CONTENT_SECTIONS.map(section => {
            let translatedTitle = section.title;

            // Translate section titles
            switch (section.title) {
                case 'Featured For You':
                    translatedTitle = t('featuredForYou');
                    break;
                case 'Storytime South Africa':
                    translatedTitle = t('storytimeSouthAfrica');
                    break;
                case 'Lullabies & Learning Songs':
                    translatedTitle = t('lullabiesLearningSongs');
                    break;
                case 'Play & Learn Activities':
                    translatedTitle = t('playLearnActivities');
                    break;
                case 'Bath Time':
                    translatedTitle = t('bathTime');
                    break;
                case 'Parent Hub':
                    translatedTitle = t('parentHub');
                    break;
            }

            return {
                ...section,
                title: translatedTitle,
                items: filteredContent.filter(item => item.category === section.category)
            };
        });
    }, [filteredContent, t]);

    const handleSearch = (query: string) => {
        updateSearchQuery(query);
    };

    return (
        <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
            <View style={styles.header}>
                <View style={styles.logoContainer}>
                    <View style={styles.logoCircle}>
                        <Text style={styles.logoEmoji}>👶</Text>
                    </View>
                    <Text style={styles.logoText}>BabyFlix SA</Text>
                </View>
                <View style={styles.headerRight}>
                    <SearchBar onSearch={handleSearch} />
                    <LanguageSelector />
                </View>
            </View>

            {searchQuery ? (
                <SearchResults results={searchResults} query={searchQuery} />
            ) : (
                <>
                    {featuredItem && <FeaturedContent item={featuredItem} />}

                    <View style={styles.content}>
                        <AgeFilter />

                        {filteredSections.map((section, index) => (
                            section.items.length > 0 && (
                                <ContentCarousel
                                    key={section.title}
                                    title={section.title}
                                    items={section.items}
                                    featured={index === 0}
                                />
                            )
                        ))}
                    </View>
                </>
            )}
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingTop: 16,
        paddingBottom: 8,
    },
    logoContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    logoCircle: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: Colors.primary,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: Colors.shadow,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 1,
        shadowRadius: 4,
        elevation: 3,
    },
    logoEmoji: {
        fontSize: 20,
    },
    logoText: {
        color: Colors.text,
        fontSize: 20,
        fontWeight: '800' as const,
        marginLeft: 12,
    },
    headerRight: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
    },
    content: {
        paddingTop: 16,
    },
});