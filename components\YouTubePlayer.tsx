import { StyleSheet, View, Dimensions } from 'react-native';
import { WebView } from 'react-native-webview';
import { Platform } from 'react-native';

interface YouTubePlayerProps {
    videoId: string;
    width?: number;
    height?: number;
}

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function YouTubePlayer({
    videoId,
    width = SCREEN_WIDTH - 32,
    height = (SCREEN_WIDTH - 32) * 0.56
}: YouTubePlayerProps) {
    if (Platform.OS === 'web') {
        // For web, use iframe
        const embedUrl = `https://www.youtube.com/embed/${videoId}?autoplay=0&rel=0&showinfo=0&controls=1`;

        return (
            <View style={[styles.container, { width, height }]}>
                <iframe
                    src={embedUrl}
                    width="100%"
                    height="100%"
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                    style={{ borderRadius: 8 }}
                />
            </View>
        );
    }

    // For mobile, use WebView
    const embedUrl = `https://www.youtube.com/embed/${videoId}?autoplay=0&rel=0&showinfo=0&controls=1&playsinline=1`;

    return (
        <View style={[styles.container, { width, height }]}>
            <WebView
                source={{ uri: embedUrl }}
                style={styles.webview}
                allowsFullscreenVideo={true}
                mediaPlaybackRequiresUserAction={false}
                javaScriptEnabled={true}
                domStorageEnabled={true}
                startInLoadingState={true}
                scalesPageToFit={true}
            />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        borderRadius: 8,
        overflow: 'hidden',
        backgroundColor: '#000',
    },
    webview: {
        flex: 1,
    },
});