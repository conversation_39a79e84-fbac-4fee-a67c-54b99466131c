export type Language = 'English' | 'isiZulu' | 'isiXhosa' | 'Afrikaans' | 'Sepedi' | 'Sesotho';

export type AgeGroup = '0-12 months' | '1-3 years' | '3-5 years' | 'All ages';

export type ContentCategory =
    | 'Storytime'
    | 'Lullabies'
    | 'Play & Learn'
    | 'Bath Time'
    | 'Parent Hub';

export interface ContentItem {
    id: string;
    title: string;
    description: string;
    thumbnail: string;
    ageGroup: AgeGroup;
    languages: Language[];
    category: ContentCategory;
    featured?: boolean;
    duration?: string;
    videoUrl?: string;
    audioUrl?: string;
    pdfUrl?: string;
    youtubeId?: string;
}

export interface CategorySection {
    title: string;
    category: ContentCategory;
    items: ContentItem[];
}