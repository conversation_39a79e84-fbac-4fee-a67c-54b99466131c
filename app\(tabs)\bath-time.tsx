import { StyleSheet, Text, View, ScrollView, Image, Pressable, Dimensions } from "react-native";
import { useRouter } from "expo-router";
import { Play, Volume2 } from "lucide-react-native";
import { useFilteredContent, useContent, useTranslation } from "@/hooks/useContentStore";
import ContentCard from "@/components/ContentCard";
import AgeFilter from "@/components/AgeFilter";
import Colors from "@/constants/colors";

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function BathTimeScreen() {
    const router = useRouter();
    const { filteredContent } = useFilteredContent();
    const { addToRecentlyViewed } = useContent();
    const t = useTranslation();

    const bathTimeContent = filteredContent.filter(
        item => item.category === 'Bath Time'
    );

    const featuredBathContent = bathTimeContent.find(item => item.featured) ||
        (bathTimeContent.length > 0 ? bathTimeContent[0] : null);

    const handleContentPress = (id: string) => {
        const item = bathTimeContent.find(item => item.id === id);
        if (item) {
            addToRecentlyViewed(item);
            router.push(`/content/${id}`);
        }
    };

    return (
        <ScrollView style={styles.container}>
            <View style={styles.header}>
                <Text style={styles.headerTitle}>{t('bathTime')}</Text>
                <Text style={styles.headerSubtitle}>
                    {t('makeBathTimeFun')}
                </Text>
            </View>

            {featuredBathContent && (
                <View style={styles.featuredContainer}>
                    <Image
                        source={{ uri: featuredBathContent.thumbnail }}
                        style={styles.featuredImage}
                        resizeMode="cover"
                    />
                    <View style={styles.featuredOverlay}>
                        <View style={styles.featuredContent}>
                            <Text style={styles.featuredTitle}>{featuredBathContent.title}</Text>
                            <Text style={styles.featuredDescription} numberOfLines={2}>
                                {featuredBathContent.description}
                            </Text>
                            <View style={styles.buttonContainer}>
                                <Pressable
                                    style={styles.playButton}
                                    onPress={() => handleContentPress(featuredBathContent.id)}
                                >
                                    <Play size={20} color={Colors.text} />
                                    <Text style={styles.buttonText}>{t('play')}</Text>
                                </Pressable>
                                <Pressable style={styles.soundButton}>
                                    <Volume2 size={20} color={Colors.text} />
                                    <Text style={styles.buttonText}>{t('sounds')}</Text>
                                </Pressable>
                            </View>
                        </View>
                    </View>
                </View>
            )}

            <View style={styles.content}>
                <AgeFilter />

                <Text style={styles.sectionTitle}>{t('bathTimeVideos')}</Text>

                <View style={styles.cardsContainer}>
                    {bathTimeContent.length > 0 ? (
                        bathTimeContent.map(item => (
                            <View key={item.id} style={styles.cardWrapper}>
                                <ContentCard
                                    item={item}
                                    width={SCREEN_WIDTH * 0.44}
                                    height={140}
                                />
                            </View>
                        ))
                    ) : (
                        <View style={styles.emptyContainer}>
                            <Text style={styles.emptyText}>
                                {t('noBathTimeAvailable')}
                            </Text>
                        </View>
                    )}
                </View>
            </View>
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.background,
    },
    header: {
        padding: 16,
    },
    headerTitle: {
        color: Colors.text,
        fontSize: 24,
        fontWeight: '700' as const,
        marginBottom: 8,
    },
    headerSubtitle: {
        color: Colors.textSecondary,
        fontSize: 16,
    },
    featuredContainer: {
        width: '100%',
        height: 240,
        marginBottom: 24,
    },
    featuredImage: {
        width: '100%',
        height: '100%',
    },
    featuredOverlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(0,0,0,0.5)',
        justifyContent: 'flex-end',
    },
    featuredContent: {
        padding: 16,
    },
    featuredTitle: {
        color: Colors.text,
        fontSize: 22,
        fontWeight: '700' as const,
        marginBottom: 8,
    },
    featuredDescription: {
        color: Colors.textSecondary,
        fontSize: 14,
        marginBottom: 16,
    },
    buttonContainer: {
        flexDirection: 'row',
    },
    playButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.primary,
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 8,
        marginRight: 12,
    },
    soundButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.secondary,
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 8,
    },
    buttonText: {
        color: Colors.text,
        fontSize: 14,
        fontWeight: '600' as const,
        marginLeft: 8,
    },
    content: {
        padding: 16,
    },
    sectionTitle: {
        color: Colors.text,
        fontSize: 18,
        fontWeight: '700' as const,
        marginTop: 16,
        marginBottom: 12,
    },
    cardsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
    },
    cardWrapper: {
        marginBottom: 16,
    },
    emptyContainer: {
        width: '100%',
        padding: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    emptyText: {
        color: Colors.textSecondary,
        fontSize: 16,
        textAlign: 'center',
    },
});